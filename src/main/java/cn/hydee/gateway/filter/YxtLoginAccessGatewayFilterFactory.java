package cn.hydee.gateway.filter;

import static cn.hydee.gateway.constants.CommonConstants.HEAD_ACC_TYPE;
import static cn.hydee.gateway.constants.CommonConstants.HEAD_EMP_CODE;
import static cn.hydee.gateway.constants.CommonConstants.HEAD_USER_ID_KEY;
import static cn.hydee.gateway.constants.CommonConstants.HEAD_USER_MERCODE;
import static cn.hydee.gateway.constants.CommonConstants.HEAD_USER_NAME_KEY;
import static cn.hydee.gateway.constants.CommonConstants.HEAD_USER_ZH_NAME_KEY;

import cn.hydee.gateway.config.ExcludeUrlConfig;
import cn.hydee.gateway.config.UserAuthConfig;
import cn.hydee.gateway.domain.JWTInfo;
import cn.hydee.gateway.domain.ReturnData;
import cn.hydee.gateway.domain.TokenForbiddenResponse;
import cn.hydee.gateway.dto.TokenDTO;
import cn.hydee.gateway.filter.YxtLoginAccessGatewayFilterFactory.Config;
import cn.hydee.gateway.util.Const;
import cn.hydee.gateway.util.UserAuthUtil;
import com.alibaba.fastjson.JSONObject;
import com.yxt.lang.constants.INumberValue;
import com.yxt.lang.util.JsonUtils;
import com.yxt.safecenter.auth.sdk.constants.AuthConstants;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpCookie;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 权限校验过滤器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Deprecated // 20250605和一心助手确认最终会废弃,全部走一心助手的网关。目前这个过滤器的流量在逐渐减少,故不在投入资源在这个过滤器上
// TODO 等流量减为0,移除Apollo YxtLoginAccess配置并删除该过滤器
public class YxtLoginAccessGatewayFilterFactory extends AbstractGatewayFilterFactory<Config> {


    /**
     * 是否要重新校验token
     */
    public static final String RE_CHECK_TOKEN = "reCheckToken";

    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private ExcludeUrlConfig excludeUrlConfig;

    @Resource
    private UserAuthConfig userAuthConfig;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private UserAuthUtil userAuthUtil;


    //针对哪些商户放行
    @Value("${token.resolver.igrone.mercodes:'SPHYDEE,hydee'}")
    private String HEAD_PASS_MERCODES = "SPHYDEE";

    public YxtLoginAccessGatewayFilterFactory() {
        super(Config.class);
    }

    /**
     * 指定拦截器顺序，在{@link cn.hydee.gateway.filter.AccessGatewayFilter}之前
     */
    @Override
    public GatewayFilter apply(Config config) {
        return new OrderedGatewayFilter((exchange, chain) -> {
            //如果不重新校验,则直接放行
            if (Boolean.FALSE.equals(exchange.getAttributes().get(AuthConstants.OLD_AUTH_FLAG))) {
                // 老的全局token鉴权过滤器也不鉴权
                exchange.getAttributes().put(RE_CHECK_TOKEN, Boolean.FALSE);
                return chain.filter(exchange);
            }
            ServerHttpRequest request = exchange.getRequest();
            String requestUri = request.getPath().pathWithinApplication().value();
            String method = request.getMethodValue();

            // 日志记录
            if (log.isDebugEnabled()) {
                log.debug("method:{} requestUri：{}", method, requestUri);
            }

            exchange.getAttributes().put(RE_CHECK_TOKEN, Boolean.TRUE);
            try {
                ServerHttpRequest.Builder mutate = request.mutate();
                // 获取用户信息
                JWTInfo user = this.getJWTInfo(request, mutate);

                // 检查是否需要拦截
                if (excludeUrlConfig.isStartWith(requestUri) || excludeUrlConfig.isEndWith(requestUri)) {
                    return chain.filter(exchange);
                }
                // 判断用户解析是否成功
                if (Objects.isNull(user)) {
                    return getVoidMono(exchange, new TokenForbiddenResponse("User Token Forbidden or Expired!"));
                }
                // 判断用户是否有效
                if (!stringRedisTemplate.opsForSet().isMember(Const.EFFECTIVE_LOGIN_USER_ID, user.getUserId())) {
                    return getVoidMono(exchange, new TokenForbiddenResponse("User Token Forbidden or Expired!"));
                }
                // 不再进行网关基础校验
                exchange.getAttributes().put(RE_CHECK_TOKEN, Boolean.FALSE);
                return chain.filter(exchange);
            } catch (Exception e) {
                //todo 上线后观察一段时间，如果服务没问题，比较稳定，error改成warn
                log.error("==== 执行YxtLoginAccess失败====，method:{} requestUri：{}", method, requestUri, e);
            }
            return chain.filter(exchange);
        }, FilterOrder.YxtLoginAccessGatewayFilter);
    }

    private JWTInfo getJWTInfo(ServerHttpRequest request, ServerHttpRequest.Builder mutate) {
        // cookie中获取不到，则会从请求头中获取Token
        String token = getToken(request);
        // 不存在token直接返回null
        if (StringUtils.isBlank(token)) {
            return null;
        }

        // 将token转换为解析对象
        String cookieRedisKey = Const.REDIS_BASE_PREFIX + this.hashToken(token);
        String tokenStr = stringRedisTemplate.opsForValue().get(cookieRedisKey);
        //token认证
        JWTInfo user;
        if (StringUtils.isNotBlank(tokenStr)) {
            TokenDTO UserDTO = JsonUtils.toObject(tokenStr, TokenDTO.class);
            user = this.convert2JWTInfo(UserDTO);
        } else {
            try {
                user = userAuthUtil.getInfoFromToken(token);
            } catch (Exception e) {
                return null;
            }
        }
        if (Objects.isNull(user)) {
            return null;
        }

        //如果是多端登录
        if (Objects.equals(user.getMultiLogin(), Boolean.FALSE.toString()) && StringUtils.isNotBlank(
            user.getUserId())) {
            Object loginTokenCache = stringRedisTemplate.opsForValue()
                .get(Const.REDIS_OAUTH_LOGIN_TOKEN_KEY + user.getUserId());
            if (Objects.isNull(loginTokenCache)) {
                // log.warn("用户{}未登录", userId)Biz;
                return null;
            }
            if (!StringUtils.equals(token, loginTokenCache.toString())) {
                // log.warn("用户{}已在其他地方登录, 当前token已失效", userId);
                return null;
            }
        }

        // 设置用户header参数
        mutate.headers(httpHeaders -> {
            httpHeaders.set(HEAD_USER_ID_KEY, user.getUserId());
            httpHeaders.set(HEAD_USER_NAME_KEY, user.getUserName());
            httpHeaders.set(HEAD_EMP_CODE, user.getEmpCode());
            try {
                httpHeaders.set(HEAD_USER_ZH_NAME_KEY, URLEncoder.encode(user.getZhName(), "utf-8"));
            } catch (UnsupportedEncodingException e) {
                log.error("Encoding error: ", e);
                throw new RuntimeException(e);
            }
            httpHeaders.set(HEAD_ACC_TYPE, user.getAccType() == null ? "" : String.valueOf(user.getAccType()));
        });

        String headerMerCode = request.getHeaders().getFirst(HEAD_USER_MERCODE);
        String tokenMerCodee = user.getMerCode();
        log.debug("打印参数据{},{}", headerMerCode, tokenMerCodee);
        if (!ObjectUtils.isEmpty(headerMerCode) && !ObjectUtils.isEmpty(tokenMerCodee) && !headerMerCode.equals(
            tokenMerCodee)) {
            if (!HEAD_PASS_MERCODES.contains(tokenMerCodee)) {
                log.debug("商户token信息不匹配{},{}", headerMerCode, tokenMerCodee);
                return null;
            }
        }
        //登录强制重置header里面的商户号
        if (!ObjectUtils.isEmpty(tokenMerCodee) && !HEAD_PASS_MERCODES.contains(tokenMerCodee)) {
            List<String> merCodes = new ArrayList<>();
            merCodes.add(tokenMerCodee);
            mutate.headers(httpHeaders -> {
                httpHeaders.put(HEAD_USER_MERCODE, merCodes);
            });
        }
        setZhNameToRedis(user);
        return user;
    }

    private JWTInfo convert2JWTInfo(TokenDTO userDTO) {
        JWTInfo jwtInfo = new JWTInfo();
        jwtInfo.setUserName(userDTO.getUserName());
        jwtInfo.setZhName(userDTO.getZhName());
        jwtInfo.setMerCode(userDTO.getMerCode());
        jwtInfo.setUserId(userDTO.getUserId());
        jwtInfo.setExprie(userDTO.getExpire());
        jwtInfo.setEmpCode(userDTO.getEmpCode());
        jwtInfo.setAccType(Optional.ofNullable(userDTO.getAccType()).map(INumberValue::value).orElse(null));
        jwtInfo.setMultiLogin(String.valueOf(userDTO.getMultiLogin()));
        return jwtInfo;
    }

    private String getToken(ServerHttpRequest request) {
        MultiValueMap<String, HttpCookie> cookies = request.getCookies();
        for (Entry<String, List<HttpCookie>> entry : cookies.entrySet()) {
            String cookieName = entry.getKey();
            //从cookie中获取当前环境的token
            if ((env + "_token").equals(cookieName) && CollectionUtils.isNotEmpty(entry.getValue())) {
                return Optional.ofNullable(entry.getValue().get(0)).map(HttpCookie::getValue).orElse(null);
            }
        }
        //cookie中获取不到，则采用老的方式从header中获取
        String authToken = request.getHeaders().getFirst(userAuthConfig.getTokenHeader());
        if (StringUtils.isBlank(authToken)) {
            authToken = request.getQueryParams().getFirst("token");
        }
        return authToken;
    }


    public static class Config {
        // 配置类，可以添加自定义配置属性，后续拓展可能会用到
    }

    /**
     * 生成字符串的md5
     *
     * @param str
     * @return java.lang.String
     * <AUTHOR>
     */
    public String hashToken(String str) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(str.getBytes());
            StringBuilder hexString = new StringBuilder();

            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 网关抛异常
     *
     * @param body 返回的数据
     */
    private Mono<Void> getVoidMono(ServerWebExchange serverWebExchange, ReturnData body) {
        serverWebExchange.getResponse().setStatusCode(HttpStatus.FORBIDDEN);
        byte[] bytes = JSONObject.toJSONString(body).getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = serverWebExchange.getResponse().bufferFactory().wrap(bytes);
        return serverWebExchange.getResponse().writeWith(Flux.just(buffer));
    }




    private void setZhNameToRedis(JWTInfo info) {
        HashOperations<String, String, String> hashOperations = stringRedisTemplate.opsForHash();
        hashOperations.put(Const.USER_ZH_NAME_KEY, info.getUserName(), info.getZhName());
    }
}