package cn.hydee.gateway.filter;

import static cn.hydee.gateway.constants.CommonConstants.GATE_WAY_PREFIX;
import static cn.hydee.gateway.constants.CommonConstants.HEAD_ACC_TYPE;
import static cn.hydee.gateway.constants.CommonConstants.HEAD_EMP_CODE;
import static cn.hydee.gateway.constants.CommonConstants.HEAD_USER_ID_KEY;
import static cn.hydee.gateway.constants.CommonConstants.HEAD_USER_MERCODE;
import static cn.hydee.gateway.constants.CommonConstants.HEAD_USER_NAME_KEY;
import static cn.hydee.gateway.constants.CommonConstants.HEAD_USER_ZH_NAME_KEY;
import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_ORIGINAL_REQUEST_URL_ATTR;

import cn.hydee.gateway.config.ExcludeUrlConfig;
import cn.hydee.gateway.config.UserAuthConfig;
import cn.hydee.gateway.domain.JWTInfo;
import cn.hydee.gateway.domain.ReturnData;
import cn.hydee.gateway.domain.TokenForbiddenResponse;
import cn.hydee.gateway.filter.context.UserInfoContext;
import cn.hydee.gateway.util.Const;
import cn.hydee.gateway.util.UserAuthUtil;
import com.alibaba.fastjson.JSONObject;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.ReactiveHashOperations;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 权限校验过滤器
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(ExcludeUrlConfig.class)
public class AccessGatewayFilter extends AbstractGatewayFilter {

  //针对哪些商户放行
  @Value("${token.resolver.igrone.mercodes:'SPHYDEE,hydee'}")
  private String HEAD_PASS_MERCODES = "SPHYDEE";

  @Autowired
  private ExcludeUrlConfig excludeUrlConfig;

  @Autowired
  private UserAuthConfig userAuthConfig;

  @Autowired
  private UserAuthUtil userAuthUtil;


  @Resource
  @Qualifier("reactiveRedisTemplate")
  private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;


  @Override
  public Mono<Void> filter(ServerWebExchange serverWebExchange,
      GatewayFilterChain gatewayFilterChain) {
    // 如果不重新校验,则直接放行
    if (Boolean.FALSE.equals(
        serverWebExchange.getAttributes().get(YxtLoginAccessGatewayFilterFactory.RE_CHECK_TOKEN))) {
      return gatewayFilterChain.filter(serverWebExchange);
    }

    // 1. 处理requestUri
    serverWebExchange.getAttributes()
        .computeIfAbsent(GATEWAY_ORIGINAL_REQUEST_URL_ATTR, s -> new LinkedHashSet<>());
    LinkedHashSet requiredAttribute = serverWebExchange.getRequiredAttribute(
        GATEWAY_ORIGINAL_REQUEST_URL_ATTR);
    ServerHttpRequest request = serverWebExchange.getRequest();
    String requestUri = request.getPath().pathWithinApplication().value();
    Iterator<URI> iterator = requiredAttribute.iterator();
    while (iterator.hasNext()) {
      URI next = iterator.next();
      if (next.getPath().startsWith(GATE_WAY_PREFIX)) {
        requestUri = next.getPath().substring(GATE_WAY_PREFIX.length());
      }
    }

    // 将变量声明为final，以便在lambda表达式中使用
    final String finalRequestUri = requestUri;

    // 2. 从Token中解析用户信息（响应式处理）
    return getJWTUserReactive(request).flatMap(userInfo -> {
      ServerHttpRequest.Builder mutate = request.mutate();
      JWTInfo user = userInfo.getUser();

      // 设置请求头信息
      if (user != null) {
        setUserHeaders(mutate, user);
      }

      // 3. 不进行登录拦截的地址
      if (excludeUrlConfig.isStartWith(finalRequestUri) || excludeUrlConfig.isEndWith(
          finalRequestUri)) {
        return gatewayFilterChain.filter(serverWebExchange);
      }

      // 4. 需要登录，判断用户token有效性
      if (user == null) {
        return forbiddenResponse(serverWebExchange,
            new TokenForbiddenResponse("User Token Forbidden or Expired!"));
      }

      // 5. 检查用户是否在有效登录用户集合中（响应式）
      return reactiveRedisTemplate.opsForSet()
          .isMember(Const.EFFECTIVE_LOGIN_USER_ID, user.getUserId())
          .flatMap(isMember -> {
            if (!isMember) {
              return forbiddenResponse(serverWebExchange,
                  new TokenForbiddenResponse("User Token Forbidden or Expired!"));
            }
            return gatewayFilterChain.filter(serverWebExchange);
          });
    });
  }


  @Override
  public int getOrder() {
    return FilterOrder.AccessGatewayFilter;
  }


  /**
   * 网关抛异常
   *
   * @param body 返回的数据
   */
  private Mono<Void> forbiddenResponse(ServerWebExchange serverWebExchange, ReturnData body) {
    serverWebExchange.getResponse().setStatusCode(HttpStatus.FORBIDDEN);
    byte[] bytes = JSONObject.toJSONString(body).getBytes(StandardCharsets.UTF_8);
    DataBuffer buffer = serverWebExchange.getResponse().bufferFactory().wrap(bytes);
    return serverWebExchange.getResponse().writeWith(Flux.just(buffer));
  }


  /**
   * 响应式获取JWT用户信息
   *
   * @param request 请求
   * @return Mono<UserInfo> 用户信息
   */
  private Mono<UserInfoContext> getJWTUserReactive(ServerHttpRequest request) {
    try {
      // 获取认证token
      String authToken = request.getHeaders().getFirst(userAuthConfig.getTokenHeader());
      if (StringUtils.isBlank(authToken)) {
        authToken = request.getQueryParams().getFirst("token");
      }
      if (authToken == null) {
        return Mono.just(new UserInfoContext(null, null));
      }

      // 解析token获取用户信息
      JWTInfo info = userAuthUtil.getInfoFromToken(authToken);
      if (info == null) {
        return Mono.just(new UserInfoContext(null, authToken));
      }

      String multiLogin = info.getMultiLogin();
      String userId = info.getUserId();
      final String finalAuthToken = authToken;

      // 判断用户是否多端登录，如果非多端登录，需要验证当前用户是否已经登录的缓存key是否和当前token一致
      if (Objects.equals(multiLogin, Boolean.FALSE.toString())) {
        return reactiveRedisTemplate.opsForValue().get(Const.REDIS_OAUTH_LOGIN_TOKEN_KEY + userId)
            .map(loginTokenCache -> {
              if (loginTokenCache == null) {
                return new UserInfoContext(null, finalAuthToken);
              }
              if (!Objects.equals(finalAuthToken, loginTokenCache)) {
                return new UserInfoContext(null, finalAuthToken);
              }
              return validateMerchantCode(request, info, finalAuthToken);
            }).defaultIfEmpty(new UserInfoContext(null, finalAuthToken));
      } else {
        // 多端登录允许，直接验证商户码
        return Mono.just(validateMerchantCode(request, info, finalAuthToken));
      }
    } catch (Exception e) {
//      log.warn("解析JWT用户信息异常", e); note: 登录之外的接口没有token就会解析失败,注释掉。和之前的逻辑保持一致
      return Mono.just(new UserInfoContext(null, null));
    }
  }

  /**
   * 验证商户码并设置用户中文名缓存
   *
   * @param request   请求
   * @param info      JWT信息
   * @param authToken 认证token
   * @return UserInfo
   */
  private UserInfoContext validateMerchantCode(ServerHttpRequest request, JWTInfo info,
      String authToken) {
    String headerMerCode = request.getHeaders().getFirst(HEAD_USER_MERCODE);
    String tokenMerCode = info.getMerCode();
    log.debug("打印参数据{},{}", headerMerCode, tokenMerCode);

    if (!ObjectUtils.isEmpty(headerMerCode) && !ObjectUtils.isEmpty(tokenMerCode)
        && !headerMerCode.equals(tokenMerCode)) {
      if (!HEAD_PASS_MERCODES.contains(tokenMerCode)) {
        log.debug("商户token信息不匹配{},{}", headerMerCode, tokenMerCode);
        return new UserInfoContext(null, authToken);
      }
    }

    // 设置用户中文名到Redis缓存
    setZhNameToRedis(info);
    return new UserInfoContext(info, authToken);
  }

  /**
   * 设置用户请求头信息
   *
   * @param mutate 请求构建者
   * @param info   JWT用户信息
   */
  private void setUserHeaders(ServerHttpRequest.Builder mutate, JWTInfo info) {
    mutate.headers(httpHeaders -> {
      httpHeaders.set(HEAD_USER_ID_KEY, info.getUserId());
      httpHeaders.set(HEAD_USER_NAME_KEY, info.getUserName());
      httpHeaders.set(HEAD_EMP_CODE, info.getEmpCode());
      try {
        httpHeaders.set(HEAD_USER_ZH_NAME_KEY,
            URLEncoder.encode(info.getZhName(), String.valueOf(StandardCharsets.UTF_8)));
      } catch (UnsupportedEncodingException e) {
        throw new RuntimeException(e);
      }
      httpHeaders.set(HEAD_ACC_TYPE,
          info.getAccType() == null ? "" : String.valueOf(info.getAccType()));
    });

    // 登录强制重置header里面的商户号
    String tokenMerCode = info.getMerCode();
    if (!ObjectUtils.isEmpty(tokenMerCode) && !HEAD_PASS_MERCODES.contains(tokenMerCode)) {
      List<String> merCodes = new ArrayList<>();
      merCodes.add(tokenMerCode);
      mutate.headers(httpHeaders -> {
        httpHeaders.put(HEAD_USER_MERCODE, merCodes);
      });
    }
  }


  private void setZhNameToRedis(JWTInfo info) {
    ReactiveHashOperations<String, String, String> hashOperations = reactiveRedisTemplate.opsForHash();
    hashOperations.put(Const.USER_ZH_NAME_KEY, info.getUserName(), info.getZhName()).subscribe();
  }
}
