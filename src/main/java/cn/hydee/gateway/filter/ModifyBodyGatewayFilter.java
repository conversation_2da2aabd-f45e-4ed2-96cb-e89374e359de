package cn.hydee.gateway.filter;

import static cn.hydee.gateway.constants.CommonConstants.HEAD_USER_MERCODE;

import cn.hydee.gateway.config.UserAuthConfig;
import cn.hydee.gateway.domain.JWTInfo;
import cn.hydee.gateway.util.UserAuthUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.net.URI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.factory.rewrite.CachedBodyOutputMessage;
import org.springframework.cloud.gateway.support.BodyInserterContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ReactiveHttpOutputMessage;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.HandlerStrategies;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Configuration
public class ModifyBodyGatewayFilter extends AbstractGatewayFilter {

    //规则是否生效
    @Value("${token.resolver.body.enable:true}")
    private boolean resolverBody = true;
    @Value("${token.resolver.igrone.mercodes:'SPHYDEE,hydee'}")
    private String HEAD_PASS_MERCODES = "SPHYDEE";

    private final static char _AND = '&';

    @Autowired
    private UserAuthConfig userAuthConfig;

    @Autowired
    private UserAuthUtil userAuthUtil;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        if (resolverBody) {
            log.debug("modifybody filter open");
            ServerHttpRequest serverHttpRequest = exchange.getRequest();
            HttpMethod method = serverHttpRequest.getMethod();
            String contentType = serverHttpRequest.getHeaders().getFirst(HttpHeaders.CONTENT_TYPE);
            try {
                String authToken = serverHttpRequest.getHeaders().getFirst(userAuthConfig.getTokenHeader());
                if (org.apache.commons.lang.StringUtils.isBlank(authToken)) {
                    authToken = serverHttpRequest.getQueryParams().getFirst("token");
                }
                if (authToken == null) {
                    return chain.filter(exchange);
                }
                JWTInfo info = userAuthUtil.getInfoFromToken(authToken);
                if (!ObjectUtils.isEmpty(info)) {
                    String authMerCode = info.getMerCode();
                    //token中包含merCode,且不为hydee和SPHYDEE这两个商户
                    if (!ObjectUtils.isEmpty(authMerCode)&&!HEAD_PASS_MERCODES.contains(authMerCode)) {
                        if (HttpMethod.POST.equals(method) && (MediaType.APPLICATION_JSON_VALUE.equalsIgnoreCase(contentType) || MediaType.APPLICATION_JSON_UTF8_VALUE.equalsIgnoreCase(contentType))) {
                            //重新构造request，参考ModifyRequestBodyGatewayFilterFactory
                            ServerRequest serverRequest = ServerRequest.create(exchange, HandlerStrategies.withDefaults().messageReaders());
                            MediaType mediaType = exchange.getRequest().getHeaders().getContentType();
                            //重点
                            Mono<String> modifiedBody = serverRequest.bodyToMono(String.class).flatMap(body -> {

                                //因为约定了终端传参的格式，所以只考虑json的情况，如果是表单传参，请自行发挥
                                if (MediaType.APPLICATION_JSON.isCompatibleWith(mediaType) || MediaType.APPLICATION_JSON_UTF8.isCompatibleWith(mediaType)) {
                                    String newBody = body;
                                    try {
                                        if (StringUtils.isEmpty(body)) {
                                            JSONObject jSONObject = JSON.parseObject(body);
                                            Object merCode = jSONObject.get("merCode");
                                            if (!ObjectUtils.isEmpty(merCode)) {
                                                // 直接put替换原键值对
                                                jSONObject.put("merCode", authMerCode);
                                                newBody = jSONObject.toString();
                                            }
                                        }

                                    } catch (Exception e) {
                                        log.error("解析报文异常", e);
                                    }
                                    return Mono.just(newBody);
                                }
                                return Mono.just(body);
                            });

                            BodyInserter<Mono<String>, ReactiveHttpOutputMessage> bodyInserter = BodyInserters.fromPublisher(
                                modifiedBody, String.class);
                            HttpHeaders headers = new HttpHeaders();
                            headers.putAll(exchange.getRequest().getHeaders());
                            //猜测这个就是之前报400错误的元凶，之前修改了body但是没有重新写content length
                            headers.remove("Content-Length");
                            CachedBodyOutputMessage outputMessage = new CachedBodyOutputMessage(
                                exchange, headers);
                            return bodyInserter.insert(outputMessage, new BodyInserterContext()).then(Mono.defer(() -> {
                                ServerHttpRequest decorator = this.decorate(exchange, headers, outputMessage);
                                return chain.filter(exchange.mutate().request(decorator).build());
                            }));
                        } else if (HttpMethod.GET.equals(method)) {
                            //GET 验签
                            URI uri = exchange.getRequest().getURI();
                            String originalQuery = uri.getRawQuery();
                            if (!StringUtils.isEmpty(originalQuery)) {
                                originalQuery = modifyOrignalQuery(originalQuery, authMerCode);
                                // 替换查询参数
                                URI newUri = UriComponentsBuilder.fromUri(uri)
                                        .replaceQuery(originalQuery)
                                        .build(true)
                                        .toUri();
                                ServerHttpRequest request = exchange.getRequest().mutate().uri(newUri).build();
                                return chain.filter(exchange.mutate().request(request).build());
                            }
                        }
                    }
                }
            } catch (Exception e) {
//                log.warn("替换body异常{}", e.getMessage());
            }

        }
        return chain.filter(exchange);
    }

    private String modifyOrignalQuery(String originalQuery, String headerMerCode) {
        if (StringUtils.isEmpty(originalQuery)) {
            return "";
        }
        String startString = "";
        String endString = "";
        int firstPos = originalQuery.indexOf(HEAD_USER_MERCODE);
        if (firstPos == -1) {
            return originalQuery;
        }
        startString = originalQuery.substring(0, firstPos);
        String paramsTempSplit = originalQuery.substring(firstPos, originalQuery.length());
        if (!StringUtils.isEmpty(paramsTempSplit)) {
            int middlePos = paramsTempSplit.indexOf(_AND);
            if (middlePos > -1 && paramsTempSplit.length() > 1) {
                String middleString = paramsTempSplit.substring(middlePos + 1, paramsTempSplit.length());
                endString = middleString;
            }
        }
        String result = startString + endString;
        if (!StringUtils.isEmpty(result) && result.charAt(result.length() - 1) != _AND) {
            result += _AND;

        }
        return result + HEAD_USER_MERCODE + "=" + headerMerCode;
    }

    @Override
    public int getOrder() {
        return FilterOrder.ModifyBodyGatewayFilter;
    }

    ServerHttpRequestDecorator decorate(ServerWebExchange exchange, HttpHeaders headers,
        CachedBodyOutputMessage outputMessage) {
        return new ServerHttpRequestDecorator(exchange.getRequest()) {
            @Override
            public HttpHeaders getHeaders() {
                long contentLength = headers.getContentLength();
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.putAll(super.getHeaders());
                if (contentLength > 0L) {
                    httpHeaders.setContentLength(contentLength);
                } else {
                    httpHeaders.set("Transfer-Encoding", "chunked");
                }
                return httpHeaders;
            }

            @Override
            public Flux<DataBuffer> getBody() {
                return outputMessage.getBody();
            }
        };
    }
}