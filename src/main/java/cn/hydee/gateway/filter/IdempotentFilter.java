package cn.hydee.gateway.filter;

import static cn.hydee.gateway.constants.CommonConstants.HEAD_USER_ID_KEY;

import cn.hydee.gateway.config.IdempotentConfig;
import cn.hydee.gateway.constants.RestCodeConstants;
import cn.hydee.gateway.domain.ReturnData;
import cn.hydee.gateway.util.Const;
import com.alibaba.fastjson.JSONObject;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 防重提交filter
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/18 13:50
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(IdempotentConfig.class)
public class IdempotentFilter implements GlobalFilter, Ordered {

    @Autowired
    private IdempotentConfig idempotentConfig;

    @Resource
    private ReactiveRedisTemplate<String, Object> reactiveRedisTemplate;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String requestUri = request.getPath().pathWithinApplication().value();
        String method = request.getMethodValue();
        if (!idempotentConfig.isContainWith(method,requestUri)) {
            // 放行
            return chain.filter(exchange);
        }
        // 从header中找到userId;
        String userId = request.getHeaders().getFirst(HEAD_USER_ID_KEY);
        // redis 新增userId对应接口
        String key = incrementKey(userId, method, requestUri);

        ReactiveValueOperations<String, Object> value = reactiveRedisTemplate.opsForValue();
        return value.increment(key)
            .flatMap(result -> {
                // 如果返回的值大于1，拒绝访问
                if (result != null && result > 1) {
                    // 阻止
                    ReturnData data = new ReturnData();
                    data.setCode(RestCodeConstants.CODE_REPEAT);
                    data.setMsg(Const.CODE_REPEAT_MSG);
                    return forbiddenIdempotent(exchange, data);
                }
                // 设置失效时间
                log.debug("设置失效时间：{} ms", idempotentConfig.getExpireMill());
                return reactiveRedisTemplate.expire(key,
                        Duration.ofMillis(idempotentConfig.getExpireMill()))
                    .then(chain.filter(exchange));
            });
    }

    @Override
    public int getOrder() {
        return FilterOrder.IdempotentFilter;
    }

    private String incrementKey(String userId,String method,String url) {
        return userId + "-" + method + "-" + url;
    }

    /**
     * 网关抛异常
     * @param body 返回的数据
     */
    private Mono<Void> forbiddenIdempotent(ServerWebExchange serverWebExchange, ReturnData body) {
        serverWebExchange.getResponse().setStatusCode(HttpStatus.OK);
        byte[] bytes = JSONObject.toJSONString(body).getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = serverWebExchange.getResponse().bufferFactory().wrap(bytes);
        return serverWebExchange.getResponse().writeWith(Flux.just(buffer));
    }
}