package cn.hydee.gateway.constants;


public class CommonConstants {

  // 用户token异常
  public static final Integer EX_USER_INVALID_CODE = 40102;
  public static final Integer EX_USER_PASS_INVALID_CODE = 40001;

  public static final String JWT_KEY_NAME = "name";
  public static final String JWT_KEY_ZH_NAME = "zh-name";
  public static final String JWT_KEY_MERCODE = "mer-code";
  /**
   * 员工编码标识
   */
  public static final String JWT_KEY_EMP_CODE = "emp-code";
  /**
   * 账号类型标识
   */
  public static final String JWT_KEY_ACC_TYPE = "acc-type";
  /**
   * 多端登录标识
   */
  public static final String JWT_KEY_MULTI_LOGIN = "multi-login";

  // 变量整理

  public final static String HEAD_USER_ID_KEY = "userId";
  public final static String HEAD_USER_NAME_KEY = "userName";
  /**
   * 用户中文名
   */
  public final static String HEAD_USER_ZH_NAME_KEY = "userZhName";
  public final static String HEAD_USER_MERCODE = "merCode";
  /**
   * 员工编码标识
   */
  public static final String HEAD_EMP_CODE = "empCode";
  /**
   * 账号类型标识
   */
  public static final String HEAD_ACC_TYPE = "accType";
  public static final String GATE_WAY_PREFIX = "/api";
}
