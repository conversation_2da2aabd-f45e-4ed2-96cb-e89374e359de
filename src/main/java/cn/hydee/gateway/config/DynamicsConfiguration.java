package cn.hydee.gateway.config;

import cn.hydee.gateway.filter.DynamicsRequestFilter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(value = "dynamic.enable", havingValue = "true")
public class DynamicsConfiguration {

    @Bean
    public DynamicsRequestFilter dynamicsRequestFilter() {
        return new DynamicsRequestFilter();
    }

}
