package cn.hydee.gateway.config;

import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;

@Slf4j
@Component
public class ApolloConfigChanged implements ApplicationContextAware {

    protected ApplicationContext applicationContext;

    @ApolloConfigChangeListener(value = {"application", "application.yml"})
    protected void changeHandler(ConfigChangeEvent changeEvent) {
        for (String key : changeEvent.changedKeys()) {
            ConfigChange change = changeEvent.getChange(key);
//            log.info("Apollo config change - {}", change.toString());
        }
        this.applicationContext.publishEvent(new EnvironmentChangeEvent(changeEvent.changedKeys()));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    static {
        Method method = null;
        try {
            method = ApolloConfigChanged.class.getDeclaredMethod("changeHandler", ConfigChangeEvent.class);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        }
        ApolloConfigChangeListener apolloConfigChangeListener = method.getAnnotation(ApolloConfigChangeListener.class);

        String nameSpaces = System.getProperty("apollo.bootstrap.namespaces");
        if (nameSpaces == null || "".equalsIgnoreCase(nameSpaces)) {
            InputStream inputStream = ApolloConfigChanged.class.getResourceAsStream("/bootstrap.properties");
            if (inputStream != null) {
                Properties properties = new Properties();
                try {
                    properties.load(inputStream);
                } catch (IOException e) {
                    e.printStackTrace();
                }
                nameSpaces = properties.getProperty("apollo.bootstrap.namespaces");
            }
        }
        if (nameSpaces == null || "".equalsIgnoreCase(nameSpaces)) {
            InputStream inputStream = ApolloConfigChanged.class.getResourceAsStream("/bootstrap.yml");
            if (inputStream != null) {
                Yaml yaml = new Yaml();
                Map<String, Object> map = yaml.loadAs(inputStream, Map.class);
                if (map.get("apollo") != null) {
                    Map<String, Object> bootstrap = (Map<String, Object>) ((Map)map.get("apollo")).get("bootstrap");
                    if (bootstrap.get("namespaces") != null) {
                        Object ymlNameSpaces = bootstrap.get("namespaces");
                        if (ymlNameSpaces instanceof String) {
                            nameSpaces = (String) ymlNameSpaces;
                        }
                        if (ymlNameSpaces instanceof List) {
                            nameSpaces = StringUtils.join((List) ymlNameSpaces, ",");
                        }
                    }
                }
            }
        }

        if (nameSpaces != null) {
            try {
                InvocationHandler invocationHandler = Proxy.getInvocationHandler(apolloConfigChangeListener);
                Field value = invocationHandler.getClass().getDeclaredField("memberValues");
                value.setAccessible(true);
                Map<String, Object> memberValues = (Map<String, Object>) value.get(invocationHandler);
                memberValues.put("value", nameSpaces.split(","));
//                log.warn("======= apolloConfigChangeListener: modified namespaces to: " + nameSpaces);
            } catch (IllegalAccessException | NoSuchFieldException e) {
                e.printStackTrace();
            }
        }
    }
}